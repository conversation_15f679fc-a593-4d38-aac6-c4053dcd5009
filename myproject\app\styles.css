/* CSS Variables for Theme Colors */
:root {
  --primary-blue: #008ba6;
  --primary-gold: #ffa100;
  --white: #ffffff;
  --black: #000000;
  --gray-light: #f5f5f5;
  --gray-dark: #333333;
  --text-primary: var(--black);
  --text-secondary: #666666;
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-light);
  --shadow: rgba(0, 0, 0, 0.1);
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --text-primary: var(--white);
  --text-secondary: #cccccc;
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --shadow: rgba(255, 255, 255, 0.1);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: all 0.3s ease;
}

html {
  scroll-behavior: smooth;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Promotional Pop-up */
.promo-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.promo-popup.show {
  opacity: 1;
  visibility: visible;
}

.promo-content {
  background: var(--bg-primary);
  border: 2px solid var(--primary-gold);
  border-radius: 15px;
  max-width: 420px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  animation: popupSlide 0.4s ease-out;
  margin: 20px;
}

@keyframes popupSlide {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.promo-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #dc3545;
  color: white;
  border: 3px solid white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  transition: all 0.3s ease;
  z-index: 10001;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.4);
}

.promo-close:hover {
  background: #c82333;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
}

.promo-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: 13px 13px 0 0;
}

.promo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.promo-text {
  padding: 25px 20px;
  text-align: center;
}

.promo-title {
  font-size: 1.5rem;
  color: var(--primary-gold);
  margin-bottom: 15px;
  font-weight: bold;
}

.promo-description {
  color: var(--text-primary);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.promo-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 25px;
}

.promo-feature {
  color: var(--text-secondary);
  font-size: 0.9rem;
  padding: 5px 0;
}

.promo-btn {
  background: linear-gradient(135deg, var(--primary-gold), #e6ac00);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.promo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px var(--shadow);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  height: 40px;
  width: 40px;
  object-fit: contain;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-blue);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-blue);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gold);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.theme-toggle {
  background: none;
  border: 2px solid var(--primary-blue);
  color: var(--primary-blue);
  padding: 8px 12px;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background: var(--primary-blue);
  color: var(--white);
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  transition: all 0.3s ease;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 100px 20px 50px;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  z-index: 2;
  position: relative;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--primary-gold);
  margin-bottom: 20px;
  font-weight: 600;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  padding: 15px 30px;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
}

.btn-primary {
  background: var(--primary-blue);
  color: var(--white);
}

.btn-primary:hover {
  background: #006d85;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 139, 166, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
}

/* Landing Back to Top Button */
.landing-back-top {
  position: fixed;
  bottom: 25px;
  right: 25px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #008ba6;
  color: white;
  border: none;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.landing-back-top:hover {
  background: #ffa100;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.landing-back-top i {
  font-size: 1.1rem;
}

/* Hero Visual Effects */
.hero-visual {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, var(--primary-gold), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--primary-blue), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--primary-gold), transparent),
    radial-gradient(1px 1px at 130px 80px, var(--primary-blue), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: twinkle 4s ease-in-out infinite alternate;
  opacity: 0.6;
}

.moon {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 100px;
  height: 100px;
  background: var(--primary-gold);
  border-radius: 50%;
  box-shadow: 0 0 50px rgba(255, 161, 0, 0.3);
  animation: float 6s ease-in-out infinite;
  transition: all 0.3s ease;
}

/* Dark mode - moon becomes gray like real moon */
html[data-theme="dark"] .moon {
  background: #c0c0c0 !important;
  box-shadow: 0 0 50px rgba(192, 192, 192, 0.3) !important;
}

/* Add moon craters/spots in dark mode */
html[data-theme="dark"] .moon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 25%, #999999 3px, transparent 4px),
    radial-gradient(circle at 60% 40%, #999999 2px, transparent 3px),
    radial-gradient(circle at 45% 70%, #999999 4px, transparent 5px),
    radial-gradient(circle at 75% 60%, #999999 2px, transparent 3px),
    radial-gradient(circle at 20% 60%, #999999 3px, transparent 4px),
    radial-gradient(circle at 80% 30%, #999999 2px, transparent 3px);
}

@keyframes twinkle {
  0% { opacity: 0.3; }
  100% { opacity: 0.8; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Section Styles */
section {
  padding: 80px 0;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 20px;
  color: var(--text-primary);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--primary-gold));
  border-radius: 2px;
}

.section-subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 50px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* About Section */
.about {
  background: var(--bg-secondary);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 50px;
  align-items: start;
}

.about-text h3 {
  color: var(--primary-blue);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.about-text p, .about-text li {
  margin-bottom: 15px;
  color: var(--text-secondary);
}

.about-text ul {
  padding-left: 20px;
}

.about-logo {
  display: flex;
  align-items: center;
  flex-direction: column; /* Susun secara menegak */
  justify-content: center;
  padding: 20px;
  text-align: center; 
  margin-top: 20px;
}

.about-logo-img {
  max-width: 300px;
  max-height: 300px;
  width: auto;
  height: auto;
  object-fit: contain;
  opacity: 0.9;
  transition: opacity 0.3s ease;
   margin-bottom: 0px;
}

.about-logo h1 {
   margin-top: 0px;
  margin-top: 10px;
}


.about-logo-img:hover {
  opacity: 1;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: var(--bg-primary);
  border-radius: 15px;
  box-shadow: 0 5px 15px var(--shadow);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--primary-blue);
  margin-bottom: 5px;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
}

/* Activities Section */
/* Activities Gallery Grid */
.activities-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 50px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.gallery-item {
  aspect-ratio: 1 / 1;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px var(--shadow);
  transition: all 0.3s ease;
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 40px var(--shadow);
}

.gallery-item .gallery-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-img {
  transform: scale(1.1);
}

.activities-action {
  text-align: center;
  margin-top: 50px;
}

.view-details-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0056b3 100%);
  color: white;
  text-decoration: none;
  padding: 14px 30px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.view-details-btn:hover {
  background: linear-gradient(135deg, var(--primary-gold) 0%, #e6b800 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
  text-decoration: none;
  color: white;
}

.view-details-btn i {
  font-size: 1.1rem;
}

.activity-card {
  background: var(--bg-primary);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow);
}

.activity-image {
  height: 200px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
}

.activity-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-img {
  transform: scale(1.1);
}

.activity-content {
  padding: 25px;
}

.activity-content h3 {
  color: var(--primary-blue);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.activity-title {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.activity-title:hover {
  color: var(--primary-gold);
  transform: translateX(5px);
}

.activity-title::after {
  content: ' 👁️';
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-title:hover::after {
  opacity: 1;
}

.activity-content p {
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.activity-details {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.activity-details span {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.activity-details i {
  color: var(--primary-gold);
}

.activity-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0056b3 100%);
  color: white;
  text-decoration: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 600;
  margin-top: 15px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.activity-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
  text-decoration: none;
  color: white;
}

/* Activity Popup Modal (Instagram-style) */
.activity-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 2000;
  justify-content: center;
  align-items: center;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-modal.active {
  display: flex;
  opacity: 1;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: 12px;
  max-width: 1000px;
  max-height: 85vh;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  animation: modalSlideIn 0.3s ease;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  z-index: 10;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-images {
  position: relative;
  background: var(--black);
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-image-slider {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-image {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  display: none;
  margin: auto;
}

.modal-image.active {
  display: block;
}

.slider-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.slider-nav:hover {
  background: rgba(0, 0, 0, 0.8);
}

.slider-prev {
  left: 10px;
}

.slider-next {
  right: 10px;
}

.slider-dots {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.slider-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.slider-dot.active {
  background: var(--primary-gold);
}

.modal-info {
  padding: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* Instagram-style header */
.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-header-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-gold);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--black);
  font-size: 14px;
}

.modal-header-info h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-header-info p {
  margin: 0;
  font-size: 12px;
  color: var(--text-secondary);
}

.modal-content-area {
  padding: 20px;
  flex: 1;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-gold);
  margin: 0 0 15px 0;
}

.modal-description {
  color: var(--text-secondary);
  margin-bottom: 25px;
  line-height: 1.6;
}

.modal-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.modal-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: var(--bg-secondary);
  border-radius: 10px;
}

.modal-detail-item i {
  color: var(--primary-gold);
  font-size: 1.1rem;
  width: 20px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Products Section */
.products {
  background: var(--bg-secondary);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 50px;
  justify-items: center;
}

.product-card {
  background: var(--bg-primary);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px var(--shadow);
  transition: transform 0.3s ease;
  position: relative;
  width: 100%;
  max-width: 400px;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--primary-gold);
  color: var(--white);
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  z-index: 2;
}

.product-image {
  height: 250px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: 15px 15px 0 0;
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
  transition: transform 0.3s ease;
}

.product-img:hover {
  transform: scale(1.05);
}

.placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.placeholder i {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--primary-blue);
}

.product-content {
  padding: 25px;
}

.product-content h3 {
  color: var(--primary-blue);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.product-content p {
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.product-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-gold);
  margin-bottom: 20px;
}

.product-btn {
  width: 100%;
}

.coming-soon .product-btn {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Contact Section */
.contact-content {
  display: flex;
  justify-content: center;
  margin-top: 50px;
}

.contact-info {
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.contact-info h3 {
  color: var(--primary-blue);
  margin-bottom: 40px;
  font-size: 1.8rem;
  text-align: center;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
}

.contact-item i {
  font-size: 1.5rem;
  color: var(--primary-gold);
  min-width: 30px;
}

.contact-item h4 {
  color: var(--text-primary);
  margin-bottom: 5px;
  font-size: 1.1rem;
  margin-right: 10px;
}

.contact-item p, .contact-item a {
  color: var(--text-secondary);
  text-decoration: none;
  margin-bottom: 3px;
  line-height: 1.4;
}

.contact-item a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}

.social-media-row {
  display: flex;
  justify-content: space-around;
  gap: 40px;
  margin-top: 20px;
}

.social-media-row .contact-item {
  flex: 1;
  max-width: 200px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px;
  border: 2px solid var(--bg-secondary);
  border-radius: 10px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background: var(--bg-secondary);
  padding: 20px 0;
  border-top: 1px solid var(--shadow);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 30px;
}

.footer-logo-img {
  height: 80%;
  width: 80px;
  object-fit: contain;
}

.footer-logo span {
  font-weight: bold;
  color: var(--primary-blue);
}

.footer-text {
  text-align: center;
  color: var(--text-secondary);
}

.footer-social {
  display: flex;
  gap: 15px;
}

.footer-social a {
  color: var(--text-secondary);
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.footer-social a:hover {
  color: var(--primary-blue);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px var(--shadow);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 50px;
    transition: left 0.3s ease;
    z-index: 999;
  }

  .nav-menu.active {
    left: 0;
  }

  /* Ensure nav-menu is visible in dark mode */
  [data-theme="dark"] .nav-menu {
    background: var(--bg-primary);
    border-right: 1px solid var(--shadow);
  }

  [data-theme="light"] .nav-menu {
    background: var(--bg-primary);
    border-right: 1px solid var(--shadow);
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .about-content {
  display: flex;
  flex-direction: column;
  align-items: center;   /* Horizontal center */
  justify-content: center; /* Vertical center */
  gap: 30px;
  text-align: center;
}

.about-content li {
  list-style: none;
}

  .about-logo-img {
    max-width: 250px;
    max-height: 250px;
    margin-bottom: 7px;
    
  }

.activities-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.activities-gallery > :nth-child(odd) {
  justify-self: end; /* Column kiri */
}

.activities-gallery > :nth-child(even) {
  justify-self: start;   /* Column kanan */
}


  .gallery-item {
    max-width: 150px;
  }

  /* Center last item if odd number */
  .gallery-item:nth-child(odd):last-child {
    grid-column: 1 / -1;
    justify-self: center;
  }

  .view-details-btn {
    padding: 15px 30px;
    font-size: 1.1rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-card {
    max-width: 100%;
  }

  /* Page Header Mobile */
  .page-header {
    padding: 100px 0 60px;
  }

  .page-title {
    font-size: 2.2rem;
    margin-bottom: 15px;
  }

  .page-subtitle {
    font-size: 1rem;
    padding: 0 15px;
    line-height: 1.5;
  }

  /* Activity Cards Mobile */
  .activity-detail-card {
    margin-bottom: 30px;
    border-radius: 15px;
  }

  .activity-detail-header {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 20px 15px;
  }

  .activity-detail-image {
    height: 180px;
    border-radius: 10px;
  }

  .activity-detail-title {
    font-size: 1.6rem;
    margin-bottom: 15px;
  }

  .activity-detail-description {
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 20px;
  }

  .activity-detail-meta {
    gap: 8px;
  }

  .activity-detail-meta span {
    font-size: 0.9rem;
  }

  /* Gallery Mobile */
  .activity-detail-content {
    padding: 0 45px 20px;
  }

  .activity-gallery h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    text-align: center;
  }
  .activity-gallery {
  width: 70%;
}

  .gallery-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px; /* jarak antara dua kolum */
  padding: 0 4px; /* tambah sedikit padding untuk tengah */
}

.gallery-grid .gallery-img {
  height: 100px;
  border-radius: 8px;
  object-fit: cover;
  width: 100%; /* penuh dalam grid cell */
}

.gallery-grid .gallery-img:nth-child(odd) {
  justify-self: end; /* kiri rapat ke tengah */
}

.gallery-grid .gallery-img:nth-child(even) {
  justify-self: start; /* kanan rapat ke tengah */
}


.gallery-img {
  height: 100px;
  border-radius: 8px;
  object-fit: cover;
  width: 95%; /* Tarik ke dalam */
  margin: 0 auto; /* Pastikan tengah */
}



  .registration-content {
    padding: 30px 20px;
    margin: 0 10px;
  }

.social-media-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 2 kolum sama rata */
  gap: 40px; 
  align-items: center;
}

.social-media-row .contact-item:first-child {
  justify-self: start; /* kolum kiri rapat kiri */
}

.social-media-row .contact-item:last-child {
  justify-self: end;   /* kolum kanan rapat kanan */
}

.contact-item {
  display: flex;              /* icon + text dalam 1 baris */
  align-items: center;
  gap: 15px;                  /* jarak antara icon dan text */
  padding: 10px 15px;
  border-radius: 10px;
}


  /* Modal responsive */
  .modal-content {
    grid-template-columns: 1fr;
    max-height: 95vh;
  }

  .modal-images {
    min-height: 300px;
  }

  .modal-content {
    grid-template-columns: 1fr;
    max-height: 95vh;
  }

  .modal-images {
    min-height: 250px;
  }

  .modal-header {
    padding: 12px 16px;
  }

  .modal-content-area {
    padding: 16px;
  }

  .modal-info {
    padding: 0;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .nav-container {
    padding: 0 15px;
  }

  .btn {
    padding: 12px 25px;
    font-size: 0.9rem;
  }

  .activities-gallery {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    justify-items: center;
  }

  .gallery-item {
    max-width: 120px;
  }

  /* Center last item if odd number - smaller screens */
  .gallery-item:nth-child(odd):last-child {
    grid-column: 1 / -1;
    justify-self: center;
  }

  .view-details-btn {
    padding: 12px 24px;
    font-size: 0.95rem;
  }

  .about-logo-img {
    max-width: 200px;
    max-height: 200px;
  }

  /* Aktiviti Page Mobile - Extra Small */
  .page-header {
    padding: 90px 0 50px;
  }

  .page-title {
    font-size: 1.8rem;
    margin-bottom: 12px;
  }

  .page-subtitle {
    font-size: 0.9rem;
    padding: 0 10px;
  }

  .activity-detail-card {
    margin-bottom: 25px;
    border-radius: 12px;
  }

  .activity-detail-header {
    padding: 15px 10px;
    gap: 12px;
  }

  .activity-detail-image {
    height: 150px;
    border-radius: 8px;
  }

  .activity-detail-title {
    font-size: 1.4rem;
    margin-bottom: 12px;
  }

  .activity-detail-description {
    font-size: 0.9rem;
    margin-bottom: 15px;
  }

  .activity-detail-meta span {
    font-size: 0.85rem;
  }

  .activity-detail-content {
    padding: 0 10px 15px;
  }

  .activity-gallery h3 {
    font-size: 1.1rem;
    margin-bottom: 12px;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .gallery-img {
    height: 80px;
    border-radius: 6px;
  }

  /* Registration Modal Mobile */
  .registration-content {
    padding: 25px 15px;
    margin: 0 5px;
    max-height: 85vh;
    border-radius: 15px;
  }

  .registration-form .form-group {
    margin-bottom: 20px;
  }

  .registration-form label {
    font-size: 0.9rem;
    margin-bottom: 6px;
  }

  .registration-form input,
  .registration-form select,
  .registration-form textarea {
    padding: 10px 12px;
    font-size: 0.9rem;
    border-radius: 8px;
  }

  .submit-btn {
    padding: 12px 25px;
    font-size: 1rem;
  }

  /* Navigation Mobile Improvements */
  .nav-container {
    padding: 0 10px;
  }

  .nav-logo-text {
    font-size: 1.1rem;
  }

  .nav-logo-img {
    width: 35px;
    height: 35px;
  }

  /* Touch-friendly improvements */
  .activity-detail-meta i {
    width: 18px;
    font-size: 0.9rem;
  }

  .modal-close {
    width: 35px;
    height: 35px;
    font-size: 1.1rem;
  }

  /* Container spacing mobile */
  .activities-detail .container {
    padding: 0 10px;
  }

  /* Back to top button mobile */
  .landing-back-top {
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
  }

  .landing-back-top i {
    font-size: 1rem;
  }

  /* Promo popup mobile */
  .promo-content {
    width: 95%;
    max-height: 90vh;
    margin: 10px;
  }

  .promo-close {
    top: -8px;
    right: -8px;
    width: 32px;
    height: 32px;
    font-size: 1.1rem;
  }

  .promo-image {
    height: 150px;
  }

  .promo-text {
    padding: 20px 15px;
  }

  .promo-title {
    font-size: 1.3rem;
  }

  .promo-description {
    font-size: 0.9rem;
  }

  .promo-btn {
    padding: 12px 25px;
    font-size: 1rem;
  }
}

/* Smooth Scrolling Enhancement */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Loading Animation */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Form Message Styles */
.form-message {
  margin-top: 15px;
  padding: 15px;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
  animation: slideIn 0.3s ease;
}

.form-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Active Navigation Link */
.nav-link.active {
  color: var(--primary-blue);
}

.nav-link.active::after {
  width: 100%;
}

/* Image Placeholder Styles */
.activity-img, .product-img {
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 1rem;
}

.activity-img::before, .product-img::before {
  content: 'Gambar akan dimuatkan di sini';
  text-align: center;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  padding: 120px 0 80px;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-gold) 0%, #e6b800 100%);
  border-radius: 2px;
}

.page-subtitle {
  font-size: 1.3rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Activities Detail Section */
.activities-detail {
  padding: 80px 0;
  background: var(--bg-secondary);
}

.activity-detail-card {
  background: var(--bg-primary);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px var(--shadow);
  margin-bottom: 50px;
  transition: transform 0.3s ease;
}

.activity-detail-card:hover {
  transform: translateY(-5px);
}

.activity-detail-header {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
  padding: 40px;
  align-items: center;
}

.activity-detail-image {
  border-radius: 15px;
  overflow: hidden;
  height: 300px;
}

.activity-detail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-detail-img:hover {
  transform: scale(1.05);
}

.activity-detail-title {
  font-size: 2.2rem;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.activity-detail-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 25px;
}

.activity-detail-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.activity-detail-meta span {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-secondary);
  font-size: 1rem;
}

.activity-detail-meta i {
  color: var(--primary-gold);
  width: 20px;
}

.activity-detail-content {
  padding: 0 40px 40px;
}

.activity-gallery {
  width: 100%;
}

.activity-gallery h3 {
  font-size: 1.4rem;
  color: var(--text-primary);
  margin-bottom: 20px;
  border-bottom: 2px solid var(--primary-gold);
  padding-bottom: 10px;
}

/* Full Width Gallery */
.activity-gallery-full {
  padding: 40px;
  background: var(--bg-secondary);
  margin: 0 -40px;
}

.activity-gallery-full h3 {
  text-align: center;
  margin-bottom: 30px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  width: 100%;
}

.gallery-img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 10px;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.gallery-img:hover {
  transform: scale(1.05);
}

.details-list {
  list-style: none;
  padding: 0;
}

.details-list li {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.details-list li:last-child {
  border-bottom: none;
}

.details-list i {
  color: var(--primary-gold);
  width: 20px;
}

.activity-registration p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.activity-registration strong {
  color: var(--primary-gold);
  font-size: 1.2rem;
}

.register-btn {
  background: linear-gradient(135deg, var(--primary-gold) 0%, #e6b800 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  justify-content: center;
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

/* Registration Modal */
.registration-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  justify-content: center;
  align-items: center;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.registration-modal.active {
  display: flex;
  opacity: 1;
}

.registration-content {
  background: var(--bg-primary);
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.registration-form {
  margin-top: 20px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-gold);
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0056b3 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  justify-content: center;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

/* Enhanced Hover Effects */
.activity-card, .product-card {
  cursor: pointer;
}

.activity-card:hover {
  box-shadow: 0 20px 40px var(--shadow);
}

.product-card.featured {
  border: 2px solid var(--primary-gold);
}

/* Responsive Text Sizes */
@media (max-width: 480px) {
  .hero-description {
    font-size: 1rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  .activity-content h3, .product-content h3 {
    font-size: 1.2rem;
  }

  .contact-info h3 {
    font-size: 1.4rem;
    margin-bottom: 30px;
  }

  .contact-item {
    margin-bottom: 15px;
  }

  .social-media-row {
    gap: 15px;
  }
}

/* Print Styles */
@media print {
  .navbar, .theme-toggle, .hamburger, .hero-visual {
    display: none;
  }

  .hero {
    min-height: auto;
    padding: 20px;
  }

  section {
    padding: 40px 0;
    page-break-inside: avoid;
  }
}
